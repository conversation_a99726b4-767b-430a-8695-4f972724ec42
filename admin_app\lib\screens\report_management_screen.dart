import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../widgets/common/loading_widget.dart';
import '../widgets/common/empty_state_widget.dart';

class ReportManagementScreen extends StatefulWidget {
  const ReportManagementScreen({super.key});

  @override
  State<ReportManagementScreen> createState() => _ReportManagementScreenState();
}

class _ReportManagementScreenState extends State<ReportManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildPostReportsTab(),
                _buildUserReportsTab(),
                _buildSystemReportsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: const BoxDecoration(
        color: AppConstants.surfaceColor,
        border: Border(
          bottom: BorderSide(
            color: AppConstants.borderColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.flag_outlined,
            size: AppConstants.iconSizeLarge,
            color: AppConstants.primaryColor,
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          const Text(
            'Report Management',
            style: TextStyle(
              fontSize: AppConstants.fontSizeHeading,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () {
              setState(() {
                _isLoading = true;
              });
              // Simulate refresh
              Future.delayed(const Duration(seconds: 1), () {
                if (mounted) {
                  setState(() {
                    _isLoading = false;
                  });
                }
              });
            },
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppConstants.surfaceColor,
      child: TabBar(
        controller: _tabController,
        labelColor: AppConstants.primaryColor,
        unselectedLabelColor: AppConstants.textSecondaryColor,
        indicatorColor: AppConstants.primaryColor,
        tabs: const [
          Tab(
            icon: Icon(Icons.post_add),
            text: 'Post Reports',
          ),
          Tab(
            icon: Icon(Icons.person_outline),
            text: 'User Reports',
          ),
          Tab(
            icon: Icon(Icons.analytics_outlined),
            text: 'System Reports',
          ),
        ],
      ),
    );
  }

  Widget _buildPostReportsTab() {
    return _buildComingSoonContent(
      icon: Icons.post_add,
      title: 'Post Reports',
      description: 'View and manage reports about posts, including inappropriate content, spam, and violations.',
    );
  }

  Widget _buildUserReportsTab() {
    return _buildComingSoonContent(
      icon: Icons.person_outline,
      title: 'User Reports',
      description: 'Handle reports about user behavior, harassment, fake accounts, and other user-related issues.',
    );
  }

  Widget _buildSystemReportsTab() {
    return _buildComingSoonContent(
      icon: Icons.analytics_outlined,
      title: 'System Reports',
      description: 'Generate analytics reports, performance metrics, and system usage statistics.',
    );
  }

  Widget _buildComingSoonContent({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 80,
              color: AppConstants.textHintColor,
            ),
            const SizedBox(height: AppConstants.paddingLarge),
            Text(
              title,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeHeading,
                fontWeight: FontWeight.bold,
                color: AppConstants.textSecondaryColor,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              description,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                color: AppConstants.textHintColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.paddingLarge),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingLarge,
                vertical: AppConstants.paddingMedium,
              ),
              decoration: BoxDecoration(
                color: AppConstants.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                border: Border.all(
                  color: AppConstants.primaryColor.withOpacity(0.3),
                ),
              ),
              child: const Text(
                'Coming Soon',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  fontWeight: FontWeight.w600,
                  color: AppConstants.primaryColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
